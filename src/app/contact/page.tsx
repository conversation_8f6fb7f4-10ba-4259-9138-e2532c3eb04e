import { strings } from '@/strings'
import { WhatsAppButton } from '@/components/WhatsAppButton'

const contactData = {
  heroTitle: 'Kontakt',
  heroSubtitle: 'Nehmen Sie Kontakt mit uns auf',
  heroDescription: 'Wir sind für Sie da - per Telefon, E-Mail oder WhatsApp',
  
  contactInfo: {
    title: 'Kontaktinformationen',
    address: {
      title: 'Adresse',
      street: 'Musterstraße 123',
      city: '8000 Zürich',
      country: 'Schweiz'
    },
    phone: {
      title: 'Telefon',
      number: '+41 44 123 45 67'
    },
    email: {
      title: 'E-Mail',
      address: '<EMAIL>'
    },
    hours: {
      title: 'Öffnungszeiten',
      weekdays: 'Mo - Fr: 08:00 - 18:00',
      saturday: 'Sa: 09:00 - 16:00',
      sunday: 'So: Geschlossen'
    }
  },
  
  services: {
    title: 'Unsere Services',
    tuning: {
      title: 'Software-Tuning',
      description: 'Professionelle Kennfeldoptimierung'
    },
    detailing: {
      title: 'Fahrzeugaufbereitung',
      description: 'Reinigung und Wiederherstellung'
    },
    consultation: {
      title: 'Beratung',
      description: 'Individuelle Fahrzeugberatung'
    }
  },
  
  cta: {
    title: 'Bereit für Ihr Projekt?',
    description: 'Kontaktieren Sie uns noch heute für eine unverbindliche Beratung',
    whatsappText: 'WhatsApp Chat',
    emailText: 'E-Mail senden',
    phoneText: 'Anrufen'
  }
}

export default function ContactPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative flex flex-col items-center justify-center min-h-[50vh] bg-cover bg-center bg-no-repeat text-white" style={{
        backgroundImage: 'linear-gradient(rgba(58, 58, 58, 0.8) 0%, rgba(58, 58, 58, 0.9) 100%), url("/DBP_M3_Lucas.jpeg")',
        filter: 'grayscale(100%)'
      }}>
        <div className="text-center p-8">
          <h1 className="text-5xl font-extrabold leading-tight tracking-tighter sm:text-6xl md:text-7xl">
            {contactData.heroTitle}
          </h1>
          <p className="mt-4 max-w-2xl mx-auto text-lg text-gray-300">
            {contactData.heroSubtitle}
          </p>
          <p className="mt-2 max-w-2xl mx-auto text-base text-gray-400">
            {contactData.heroDescription}
          </p>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-8 sm:py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-extrabold text-anthracite sm:text-4xl">{contactData.contactInfo.title}</h2>
            <p className="mt-4 text-lg text-gray-600">Alle Wege führen zu DB-Performance</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Address */}
            <div className="bg-gray-50 rounded-lg shadow-md p-6 text-center">
              <div className="bg-accent-red text-white rounded-full p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-anthracite mb-2">{contactData.contactInfo.address.title}</h3>
              <p className="text-gray-600">{contactData.contactInfo.address.street}</p>
              <p className="text-gray-600">{contactData.contactInfo.address.city}</p>
              <p className="text-gray-600">{contactData.contactInfo.address.country}</p>
            </div>

            {/* Phone */}
            <div className="bg-gray-50 rounded-lg shadow-md p-6 text-center">
              <div className="bg-accent-red text-white rounded-full p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-anthracite mb-2">{contactData.contactInfo.phone.title}</h3>
              <a href={`tel:${contactData.contactInfo.phone.number}`} className="text-accent-red hover:underline font-semibold">
                {contactData.contactInfo.phone.number}
              </a>
            </div>

            {/* Email */}
            <div className="bg-gray-50 rounded-lg shadow-md p-6 text-center">
              <div className="bg-accent-red text-white rounded-full p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-anthracite mb-2">{contactData.contactInfo.email.title}</h3>
              <a href={`mailto:${contactData.contactInfo.email.address}`} className="text-accent-red hover:underline font-semibold">
                {contactData.contactInfo.email.address}
              </a>
            </div>

            {/* Hours */}
            <div className="bg-gray-50 rounded-lg shadow-md p-6 text-center">
              <div className="bg-accent-red text-white rounded-full p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-anthracite mb-2">{contactData.contactInfo.hours.title}</h3>
              <p className="text-gray-600">{contactData.contactInfo.hours.weekdays}</p>
              <p className="text-gray-600">{contactData.contactInfo.hours.saturday}</p>
              <p className="text-gray-600">{contactData.contactInfo.hours.sunday}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-8 sm:py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-extrabold text-anthracite sm:text-4xl">{contactData.services.title}</h2>
            <p className="mt-4 text-lg text-gray-600">Sprechen Sie uns auf unsere Spezialbereiche an</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-md p-6 text-center transition-transform hover:scale-105">
              <div className="bg-accent-red text-white rounded-full p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-anthracite mb-2">{contactData.services.tuning.title}</h3>
              <p className="text-gray-600">{contactData.services.tuning.description}</p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6 text-center transition-transform hover:scale-105">
              <div className="bg-accent-red text-white rounded-full p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-anthracite mb-2">{contactData.services.detailing.title}</h3>
              <p className="text-gray-600">{contactData.services.detailing.description}</p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6 text-center transition-transform hover:scale-105">
              <div className="bg-accent-red text-white rounded-full p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-anthracite mb-2">{contactData.services.consultation.title}</h3>
              <p className="text-gray-600">{contactData.services.consultation.description}</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-anthracite text-white py-8 sm:py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-extrabold sm:text-4xl">
            {contactData.cta.title}
          </h2>
          <p className="mt-4 text-lg text-gray-300">
            {contactData.cta.description}
          </p>
          <div className="mt-4 flex flex-col sm:flex-row gap-4 justify-center">
            <WhatsAppButton
              className="flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-md h-12 px-6 bg-green-600 text-white text-base font-bold shadow-xl transition-transform hover:scale-105"
              message={strings.contact.whatsapp.defaultMessage}
              phoneNumber={strings.contact.company.whatsapp}
            >
              {contactData.cta.whatsappText}
            </WhatsAppButton>
            
            <a
              href="mailto:<EMAIL>"
              className="flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-md h-12 px-6 bg-accent-red text-white text-base font-bold shadow-xl transition-transform hover:scale-105"
            >
              {contactData.cta.emailText}
            </a>

            <a
              href="tel:+41441234567"
              className="flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-md h-12 px-6 bg-anthracite border-2 border-white text-white text-base font-bold shadow-xl transition-transform hover:scale-105"
            >
              {contactData.cta.phoneText}
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}
