'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { cookieManager, CookieCategory, COOKIE_DEFINITIONS } from '@/lib/cookieManager'

export const metadata = {
  title: 'Cookie-Einstellungen | DB-Performance Garage Bytyci',
  description: 'Verwalten Sie Ihre Cookie-Präferenzen und Datenschutzeinstellungen für die DB-Performance Website.',
  robots: {
    index: true,
    follow: true,
  },
}

export default function CookieSettingsPage() {
  const [preferences, setPreferences] = useState<Record<CookieCategory, boolean>>({
    necessary: true,
    functional: false,
    analytics: false,
    marketing: false,
  })
  const [isLoading, setIsLoading] = useState(true)
  const [showSuccess, setShowSuccess] = useState(false)

  useEffect(() => {
    const consent = cookieManager.getConsent()
    setPreferences(consent.preferences)
    setIsLoading(false)
  }, [])

  const handleCategoryChange = (category: CookieCategory, enabled: boolean) => {
    if (category === 'necessary') return // Necessary cookies cannot be disabled
    setPreferences(prev => ({ ...prev, [category]: enabled }))
  }

  const handleSavePreferences = () => {
    cookieManager.setConsent(preferences)
    setShowSuccess(true)
    setTimeout(() => setShowSuccess(false), 3000)
  }

  const handleAcceptAll = () => {
    const allPreferences = {
      necessary: true,
      functional: true,
      analytics: true,
      marketing: true,
    }
    setPreferences(allPreferences)
    cookieManager.setConsent(allPreferences)
    setShowSuccess(true)
    setTimeout(() => setShowSuccess(false), 3000)
  }

  const handleRejectAll = () => {
    const minimalPreferences = {
      necessary: true,
      functional: false,
      analytics: false,
      marketing: false,
    }
    setPreferences(minimalPreferences)
    cookieManager.setConsent(minimalPreferences)
    setShowSuccess(true)
    setTimeout(() => setShowSuccess(false), 3000)
  }

  const getCookiesByCategory = (category: CookieCategory) => {
    return COOKIE_DEFINITIONS.filter(cookie => cookie.category === category)
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-6 py-16">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-300 rounded w-2/3 mb-8"></div>
          <div className="space-y-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-32 bg-gray-300 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-6 py-16">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-dark-gray mb-4">Cookie-Einstellungen</h1>
          <p className="text-gray-600 leading-relaxed">
            Hier können Sie Ihre Cookie-Präferenzen verwalten. Sie können jederzeit Ihre Einstellungen ändern. 
            Beachten Sie, dass das Deaktivieren bestimmter Cookies die Funktionalität der Website beeinträchtigen kann.
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Weitere Informationen finden Sie in unserer{' '}
            <Link href="/datenschutz" className="text-accent-red hover:underline">
              Datenschutzerklärung
            </Link>
          </p>
        </div>

        {/* Success Message */}
        {showSuccess && (
          <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              Ihre Cookie-Einstellungen wurden erfolgreich gespeichert.
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="mb-8 p-6 bg-gray-50 rounded-lg">
          <h2 className="text-lg font-semibold mb-4">Schnellaktionen</h2>
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={handleAcceptAll}
              className="px-6 py-2 bg-accent-red hover:bg-red-600 text-white font-medium rounded transition-colors"
            >
              Alle Cookies akzeptieren
            </button>
            <button
              onClick={handleRejectAll}
              className="px-6 py-2 border border-gray-300 text-gray-700 hover:bg-gray-100 rounded transition-colors"
            >
              Nur notwendige Cookies
            </button>
          </div>
        </div>

        {/* Cookie Categories */}
        <div className="space-y-6">
          {/* Necessary Cookies */}
          <div className="border border-gray-200 rounded-lg p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-dark-gray mb-2">
                  Notwendige Cookies
                  <span className="ml-2 px-2 py-1 text-xs bg-gray-200 text-gray-600 rounded">
                    Immer aktiv
                  </span>
                </h3>
                <p className="text-gray-600">
                  Diese Cookies sind für das Funktionieren der Website unerlässlich. Sie ermöglichen grundlegende 
                  Funktionen wie Seitennavigation und Zugriff auf sichere Bereiche der Website. Ohne diese Cookies 
                  kann die Website nicht ordnungsgemäß funktionieren.
                </p>
              </div>
              <div className="ml-4">
                <input
                  type="checkbox"
                  checked={true}
                  disabled={true}
                  className="w-5 h-5 text-accent-red bg-gray-100 border-gray-300 rounded focus:ring-accent-red"
                />
              </div>
            </div>
            <div className="space-y-2">
              {getCookiesByCategory('necessary').map(cookie => (
                <div key={cookie.name} className="text-sm bg-gray-50 p-3 rounded">
                  <div className="font-medium">{cookie.name}</div>
                  <div className="text-gray-600">{cookie.purpose}</div>
                  <div className="text-gray-500">Dauer: {cookie.duration} | Anbieter: {cookie.provider}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Functional Cookies */}
          <div className="border border-gray-200 rounded-lg p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-dark-gray mb-2">Funktionale Cookies</h3>
                <p className="text-gray-600">
                  Diese Cookies ermöglichen erweiterte Funktionen und Personalisierung der Website. Sie können 
                  von uns oder von Drittanbietern gesetzt werden, deren Dienste wir auf unseren Seiten verwenden.
                </p>
              </div>
              <div className="ml-4">
                <input
                  type="checkbox"
                  checked={preferences.functional}
                  onChange={(e) => handleCategoryChange('functional', e.target.checked)}
                  className="w-5 h-5 text-accent-red bg-gray-100 border-gray-300 rounded focus:ring-accent-red"
                />
              </div>
            </div>
            <div className="space-y-2">
              {getCookiesByCategory('functional').map(cookie => (
                <div key={cookie.name} className="text-sm bg-gray-50 p-3 rounded">
                  <div className="font-medium">{cookie.name}</div>
                  <div className="text-gray-600">{cookie.purpose}</div>
                  <div className="text-gray-500">Dauer: {cookie.duration} | Anbieter: {cookie.provider}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Analytics Cookies */}
          <div className="border border-gray-200 rounded-lg p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-dark-gray mb-2">Analyse-Cookies</h3>
                <p className="text-gray-600">
                  Diese Cookies helfen uns zu verstehen, wie Besucher mit der Website interagieren, indem sie 
                  Informationen anonym sammeln und melden. Diese Informationen helfen uns, die Website zu verbessern.
                </p>
              </div>
              <div className="ml-4">
                <input
                  type="checkbox"
                  checked={preferences.analytics}
                  onChange={(e) => handleCategoryChange('analytics', e.target.checked)}
                  className="w-5 h-5 text-accent-red bg-gray-100 border-gray-300 rounded focus:ring-accent-red"
                />
              </div>
            </div>
            <div className="space-y-2">
              {getCookiesByCategory('analytics').map(cookie => (
                <div key={cookie.name} className="text-sm bg-gray-50 p-3 rounded">
                  <div className="font-medium">{cookie.name}</div>
                  <div className="text-gray-600">{cookie.purpose}</div>
                  <div className="text-gray-500">Dauer: {cookie.duration} | Anbieter: {cookie.provider}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Marketing Cookies */}
          <div className="border border-gray-200 rounded-lg p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-dark-gray mb-2">Marketing-Cookies</h3>
                <p className="text-gray-600">
                  Diese Cookies werden verwendet, um Ihnen relevante Werbung zu zeigen. Sie können auch verwendet 
                  werden, um die Anzahl der Anzeigen zu begrenzen und die Wirksamkeit von Werbekampagnen zu messen.
                </p>
              </div>
              <div className="ml-4">
                <input
                  type="checkbox"
                  checked={preferences.marketing}
                  onChange={(e) => handleCategoryChange('marketing', e.target.checked)}
                  className="w-5 h-5 text-accent-red bg-gray-100 border-gray-300 rounded focus:ring-accent-red"
                />
              </div>
            </div>
            <div className="space-y-2">
              {getCookiesByCategory('marketing').map(cookie => (
                <div key={cookie.name} className="text-sm bg-gray-50 p-3 rounded">
                  <div className="font-medium">{cookie.name}</div>
                  <div className="text-gray-600">{cookie.purpose}</div>
                  <div className="text-gray-500">Dauer: {cookie.duration} | Anbieter: {cookie.provider}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={handleSavePreferences}
            className="px-8 py-3 bg-accent-red hover:bg-red-600 text-white font-medium rounded transition-colors"
          >
            Einstellungen speichern
          </button>
          <Link
            href="/"
            className="px-8 py-3 text-center border border-gray-300 text-gray-700 hover:bg-gray-100 rounded transition-colors"
          >
            Zurück zur Startseite
          </Link>
        </div>

        {/* Additional Information */}
        <div className="mt-12 p-6 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Weitere Informationen</h3>
          <div className="text-blue-800 space-y-2">
            <p>
              • Sie können Ihre Cookie-Einstellungen jederzeit über diese Seite ändern
            </p>
            <p>
              • Cookies können auch direkt in Ihrem Browser verwaltet werden
            </p>
            <p>
              • Das Löschen von Cookies kann dazu führen, dass Sie sich erneut anmelden müssen
            </p>
            <p>
              • Bei Fragen zum Datenschutz kontaktieren Sie uns über unser{' '}
              <Link href="/contact" className="underline">Kontaktformular</Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
