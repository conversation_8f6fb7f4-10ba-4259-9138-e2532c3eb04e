'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'

export const CookieBanner = () => {
  const [showBanner, setShowBanner] = useState(false)

  useEffect(() => {
    const cookieConsent = localStorage.getItem('cookie_consent')
    if (cookieConsent !== 'true') {
      setShowBanner(true)
    }
  }, [])

  const acceptCookies = () => {
    localStorage.setItem('cookie_consent', 'true')
    setShowBanner(false)
  }

  const declineCookies = () => {
    localStorage.setItem('cookie_consent', 'false')
    setShowBanner(false)
  }

  if (!showBanner) {
    return null
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-gray-800 text-white p-4 z-50">
      <div className="container mx-auto flex flex-col md:flex-row justify-between items-center">
        <p className="text-sm mb-4 md:mb-0">
          Wir verwenden Cookies, um Ihre Erfahrung auf unserer Website zu verbessern. Bitte lesen Sie unsere{' '}
          <Link href="/datenschutz" className="underline">
            Datenschutzerklärung
          </Link>
          , um mehr zu erfahren.
        </p>
        <div className="flex gap-4">
          <button
            onClick={acceptCookies}
            className="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded"
          >
            Akzeptieren
          </button>
          <button
            onClick={declineCookies}
            className="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded"
          >
            Ablehnen
          </button>
        </div>
      </div>
    </div>
  )
}
