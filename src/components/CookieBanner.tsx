'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { cookieManager, CookieCategory } from '@/lib/cookieManager'

interface CookieBannerProps {
  onSettingsClick?: () => void
}

export const CookieBanner = ({ onSettingsClick }: CookieBannerProps) => {
  const [showBanner, setShowBanner] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [preferences, setPreferences] = useState<Record<CookieCategory, boolean>>({
    necessary: true,
    functional: false,
    analytics: false,
    marketing: false,
  })

  useEffect(() => {
    const consent = cookieManager.getConsent()
    if (!consent.hasConsented) {
      setShowBanner(true)
      // Load current preferences if any exist
      setPreferences(consent.preferences)
    }
  }, [])

  const handleAcceptAll = () => {
    const allPreferences = {
      necessary: true,
      functional: true,
      analytics: true,
      marketing: true,
    }
    cookieManager.setConsent(allPreferences)
    setShowBanner(false)
  }

  const handleRejectAll = () => {
    const minimalPreferences = {
      necessary: true,
      functional: false,
      analytics: false,
      marketing: false,
    }
    cookieManager.setConsent(minimalPreferences)
    setShowBanner(false)
  }

  const handleSavePreferences = () => {
    cookieManager.setConsent(preferences)
    setShowBanner(false)
  }

  const handleCategoryChange = (category: CookieCategory, enabled: boolean) => {
    if (category === 'necessary') return // Necessary cookies cannot be disabled
    setPreferences(prev => ({ ...prev, [category]: enabled }))
  }

  if (!showBanner) {
    return null
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-dark-gray text-white shadow-2xl z-50 border-t border-gray-600">
      <div className="container mx-auto p-6">
        {!showDetails ? (
          // Simple banner view
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
            <div className="flex-1">
              <h3 className="text-lg font-semibold mb-2">Cookie-Einstellungen</h3>
              <p className="text-sm text-gray-300 leading-relaxed">
                Wir verwenden Cookies, um Ihre Erfahrung auf unserer Website zu verbessern und um Ihnen
                personalisierte Inhalte und Werbung anzuzeigen. Einige Cookies sind für das Funktionieren
                der Website erforderlich, während andere uns helfen, die Website zu analysieren und zu verbessern.{' '}
                <Link href="/datenschutz" className="text-accent-red hover:underline">
                  Mehr in unserer Datenschutzerklärung
                </Link>
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 min-w-fit">
              <button
                onClick={() => setShowDetails(true)}
                className="px-4 py-2 text-sm border border-gray-500 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors rounded"
              >
                Einstellungen
              </button>
              <button
                onClick={handleRejectAll}
                className="px-4 py-2 text-sm border border-gray-500 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors rounded"
              >
                Nur notwendige
              </button>
              <button
                onClick={handleAcceptAll}
                className="px-6 py-2 text-sm bg-accent-red hover:bg-red-600 text-white font-medium transition-colors rounded"
              >
                Alle akzeptieren
              </button>
            </div>
          </div>
        ) : (
          // Detailed settings view
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-semibold">Cookie-Einstellungen verwalten</h3>
              <button
                onClick={() => setShowDetails(false)}
                className="text-gray-400 hover:text-white text-2xl"
                aria-label="Schließen"
              >
                ×
              </button>
            </div>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {/* Necessary Cookies */}
              <div className="border border-gray-600 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="font-medium text-white">Notwendige Cookies</h4>
                    <p className="text-sm text-gray-300 mt-1">
                      Diese Cookies sind für das Funktionieren der Website unerlässlich und können nicht deaktiviert werden.
                    </p>
                  </div>
                  <div className="ml-4">
                    <input
                      type="checkbox"
                      checked={true}
                      disabled={true}
                      className="w-4 h-4 text-accent-red bg-gray-600 border-gray-500 rounded focus:ring-accent-red"
                    />
                  </div>
                </div>
              </div>

              {/* Functional Cookies */}
              <div className="border border-gray-600 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="font-medium text-white">Funktionale Cookies</h4>
                    <p className="text-sm text-gray-300 mt-1">
                      Diese Cookies ermöglichen erweiterte Funktionen und Personalisierung der Website.
                    </p>
                  </div>
                  <div className="ml-4">
                    <input
                      type="checkbox"
                      checked={preferences.functional}
                      onChange={(e) => handleCategoryChange('functional', e.target.checked)}
                      className="w-4 h-4 text-accent-red bg-gray-600 border-gray-500 rounded focus:ring-accent-red"
                    />
                  </div>
                </div>
              </div>

              {/* Analytics Cookies */}
              <div className="border border-gray-600 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="font-medium text-white">Analyse-Cookies</h4>
                    <p className="text-sm text-gray-300 mt-1">
                      Diese Cookies helfen uns zu verstehen, wie Besucher mit der Website interagieren.
                    </p>
                  </div>
                  <div className="ml-4">
                    <input
                      type="checkbox"
                      checked={preferences.analytics}
                      onChange={(e) => handleCategoryChange('analytics', e.target.checked)}
                      className="w-4 h-4 text-accent-red bg-gray-600 border-gray-500 rounded focus:ring-accent-red"
                    />
                  </div>
                </div>
              </div>

              {/* Marketing Cookies */}
              <div className="border border-gray-600 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="font-medium text-white">Marketing-Cookies</h4>
                    <p className="text-sm text-gray-300 mt-1">
                      Diese Cookies werden verwendet, um Ihnen relevante Werbung zu zeigen.
                    </p>
                  </div>
                  <div className="ml-4">
                    <input
                      type="checkbox"
                      checked={preferences.marketing}
                      onChange={(e) => handleCategoryChange('marketing', e.target.checked)}
                      className="w-4 h-4 text-accent-red bg-gray-600 border-gray-500 rounded focus:ring-accent-red"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-600">
              <Link
                href="/cookie-einstellungen"
                className="px-4 py-2 text-sm text-center border border-gray-500 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors rounded"
              >
                Detaillierte Einstellungen
              </Link>
              <button
                onClick={handleRejectAll}
                className="px-4 py-2 text-sm border border-gray-500 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors rounded"
              >
                Nur notwendige
              </button>
              <button
                onClick={handleSavePreferences}
                className="px-6 py-2 text-sm bg-accent-red hover:bg-red-600 text-white font-medium transition-colors rounded"
              >
                Auswahl speichern
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
