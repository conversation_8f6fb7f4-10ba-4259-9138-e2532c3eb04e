'use client'

import { useEffect } from 'react'
import Script from 'next/script'
import { useAnalyticsConsent } from '@/hooks/useCookieConsent'

interface GoogleAnalyticsProps {
  measurementId: string
}

export const GoogleAnalytics = ({ measurementId }: GoogleAnalyticsProps) => {
  const { isAllowed, isLoading } = useAnalyticsConsent()

  useEffect(() => {
    if (!isLoading && typeof window !== 'undefined') {
      // Initialize Google Analytics with consent mode
      if ((window as any).gtag) {
        (window as any).gtag('consent', 'default', {
          analytics_storage: isAllowed ? 'granted' : 'denied',
          ad_storage: 'denied', // Will be handled by marketing consent
          ad_user_data: 'denied',
          ad_personalization: 'denied',
          wait_for_update: 500,
        })

        // Configure Google Analytics
        (window as any).gtag('config', measurementId, {
          page_title: document.title,
          page_location: window.location.href,
        })
      }
    }
  }, [isAllowed, isLoading, measurementId])

  // Don't load scripts if consent is not given
  if (isLoading || !isAllowed) {
    return null
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          
          // Set default consent state
          gtag('consent', 'default', {
            analytics_storage: 'granted',
            ad_storage: 'denied',
            ad_user_data: 'denied',
            ad_personalization: 'denied',
            wait_for_update: 500,
          });
          
          gtag('config', '${measurementId}', {
            page_title: document.title,
            page_location: window.location.href,
          });
        `}
      </Script>
    </>
  )
}

/**
 * Facebook Pixel component with cookie consent integration
 */
interface FacebookPixelProps {
  pixelId: string
}

export const FacebookPixel = ({ pixelId }: FacebookPixelProps) => {
  const { isAllowed, isLoading } = useAnalyticsConsent() // Using analytics for now, could be marketing

  useEffect(() => {
    if (!isLoading && isAllowed && typeof window !== 'undefined') {
      // Initialize Facebook Pixel
      if (!(window as any).fbq) {
        const fbq = function(...args: any[]) {
          if ((fbq as any).callMethod) {
            (fbq as any).callMethod.apply(fbq, args)
          } else {
            (fbq as any).queue.push(args)
          }
        };
        (fbq as any).push = fbq;
        (fbq as any).loaded = true;
        (fbq as any).version = '2.0';
        (fbq as any).queue = [];
        (window as any).fbq = fbq;
      }

      // Load Facebook Pixel script
      const script = document.createElement('script')
      script.async = true
      script.src = 'https://connect.facebook.net/en_US/fbevents.js'
      document.head.appendChild(script)

      // Initialize pixel
      ;(window as any).fbq('init', pixelId)
      ;(window as any).fbq('track', 'PageView')
    }
  }, [isAllowed, isLoading, pixelId])

  if (isLoading || !isAllowed) {
    return null
  }

  return (
    <Script id="facebook-pixel" strategy="afterInteractive">
      {`
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        
        fbq('init', '${pixelId}');
        fbq('track', 'PageView');
      `}
    </Script>
  )
}

/**
 * Generic tracking script component with cookie consent
 */
interface ConsentTrackingScriptProps {
  category: 'analytics' | 'marketing' | 'functional'
  src?: string
  children?: string
  id: string
}

export const ConsentTrackingScript = ({ 
  category, 
  src, 
  children, 
  id 
}: ConsentTrackingScriptProps) => {
  const { isAllowed, isLoading } = useAnalyticsConsent() // This should be dynamic based on category

  if (isLoading || !isAllowed) {
    return null
  }

  if (src) {
    return <Script src={src} strategy="afterInteractive" id={id} />
  }

  if (children) {
    return (
      <Script id={id} strategy="afterInteractive">
        {children}
      </Script>
    )
  }

  return null
}
